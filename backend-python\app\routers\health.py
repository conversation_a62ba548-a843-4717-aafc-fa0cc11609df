"""
Health check endpoints
"""
from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Vision Assistant API"
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check with system information
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Vision Assistant API",
        "version": "1.0.0",
        "components": {
            "api": "healthy",
            "yolo_model": "not_loaded",  # Se actualizará cuando se cargue el modelo
            "speech_recognition": "available"
        }
    }
