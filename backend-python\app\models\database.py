"""
Database configuration and models
"""
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from app.core.config import settings

# Database URL - usar SQLite para desarrollo local
DATABASE_URL = settings.DATABASE_URL or "sqlite:///./vision_assistant.db"

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

class User(Base):
    """User model for storing user information"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)
    voice_profile = Column(Text, nullable=True)  # JSON string for voice characteristics
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_seen = Column(DateTime(timezone=True), server_default=func.now())
    is_active = Column(Boolean, default=True)
    
    # Preferences
    preferred_language = Column(String(10), default="es-ES")
    voice_speed = Column(Float, default=0.8)
    confidence_threshold = Column(Float, default=0.5)

class DetectionSession(Base):
    """Detection session model for storing detection history"""
    __tablename__ = "detection_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), nullable=False)
    
    # Detection results
    persons_detected = Column(Integer, default=0)
    total_objects = Column(Integer, default=0)
    detection_data = Column(Text, nullable=True)  # JSON string with full detection results
    
    # Session info
    image_path = Column(String(255), nullable=True)
    processing_time = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class VoiceCommand(Base):
    """Voice command model for storing voice interaction history"""
    __tablename__ = "voice_commands"
    
    id = Column(Integer, primary_key=True, index=True)
    command_id = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), nullable=False)
    
    # Voice data
    recognized_text = Column(Text, nullable=False)
    command_type = Column(String(50), nullable=False)
    confidence_score = Column(Float, nullable=True)
    
    # Response
    response_text = Column(Text, nullable=True)
    action_taken = Column(String(100), nullable=True)
    
    # Timing
    processing_time = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create tables
def create_tables():
    Base.metadata.create_all(bind=engine)
