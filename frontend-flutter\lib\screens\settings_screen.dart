import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/voice_provider.dart';
import '../services/api_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ApiService _apiService = ApiService();
  Map<String, dynamic>? _healthStatus;
  List<String>? _availableCommands;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final health = await _apiService.getHealthStatus();
      final commands = await _apiService.getAvailableCommands();
      
      setState(() {
        _healthStatus = health;
        _availableCommands = commands;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error cargando datos: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuración'),
        backgroundColor: Colors.blue,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // App Info Section
                _buildSection(
                  'Información de la App',
                  [
                    ListTile(
                      leading: const Icon(Icons.info),
                      title: const Text('Versión'),
                      subtitle: const Text('1.0.0'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.description),
                      title: const Text('Descripción'),
                      subtitle: const Text(
                        'Aplicación móvil de asistencia visual con comandos de voz',
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Voice Settings Section
                _buildSection(
                  'Configuración de Voz',
                  [
                    Consumer<VoiceProvider>(
                      builder: (context, voiceProvider, child) {
                        return ListTile(
                          leading: const Icon(Icons.mic),
                          title: const Text('Estado del Micrófono'),
                          subtitle: Text(
                            voiceProvider.speechEnabled
                                ? 'Habilitado'
                                : 'Deshabilitado',
                          ),
                          trailing: Icon(
                            voiceProvider.speechEnabled
                                ? Icons.check_circle
                                : Icons.error,
                            color: voiceProvider.speechEnabled
                                ? Colors.green
                                : Colors.red,
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.language),
                      title: const Text('Idioma'),
                      subtitle: const Text('Español (España)'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.volume_up),
                      title: const Text('Respuestas de Voz'),
                      subtitle: const Text('Habilitadas'),
                      trailing: Switch(
                        value: true,
                        onChanged: (value) {
                          // TODO: Implement voice response toggle
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // API Status Section
                if (_healthStatus != null)
                  _buildSection(
                    'Estado del Servidor',
                    [
                      ListTile(
                        leading: Icon(
                          Icons.cloud,
                          color: _healthStatus!['status'] == 'healthy'
                              ? Colors.green
                              : Colors.red,
                        ),
                        title: const Text('Conexión API'),
                        subtitle: Text(_healthStatus!['status'] ?? 'Desconocido'),
                      ),
                      ListTile(
                        leading: const Icon(Icons.access_time),
                        title: const Text('Última actualización'),
                        subtitle: Text(_healthStatus!['timestamp'] ?? 'N/A'),
                      ),
                    ],
                  ),

                const SizedBox(height: 20),

                // Available Commands Section
                if (_availableCommands != null)
                  _buildSection(
                    'Comandos Disponibles',
                    [
                      ..._availableCommands!.map(
                        (command) => ListTile(
                          leading: const Icon(Icons.keyboard_voice),
                          title: Text('"$command"'),
                        ),
                      ),
                    ],
                  ),

                const SizedBox(height: 20),

                // Actions Section
                _buildSection(
                  'Acciones',
                  [
                    ListTile(
                      leading: const Icon(Icons.refresh),
                      title: const Text('Actualizar Estado'),
                      onTap: _loadData,
                    ),
                    ListTile(
                      leading: const Icon(Icons.mic_off),
                      title: const Text('Reinicializar Voz'),
                      onTap: () {
                        context.read<VoiceProvider>().initializeSpeech();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Voz reinicializada'),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.help),
                      title: const Text('Ayuda'),
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Ayuda'),
                            content: const Text(
                              'Para usar la aplicación:\n\n'
                              '1. Toca el botón del micrófono\n'
                              '2. Di un comando como "detectar personas"\n'
                              '3. Toma una foto o selecciona de la galería\n'
                              '4. Escucha los resultados\n\n'
                              'Comandos disponibles:\n'
                              '• "detectar personas"\n'
                              '• "buscar objetos"\n'
                              '• "contar personas"\n'
                              '• "ayuda"',
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Cerrar'),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }
}
