import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../providers/user_provider.dart';
import '../providers/voice_provider.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _permissionsGranted = false;
  bool _isIntroducing = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initializeApp() async {
    // Request permissions
    await _requestPermissions();

    // Initialize providers
    await context.read<VoiceProvider>().initializeSpeech();
    await context.read<UserProvider>().initializeUser();

    // Start animations
    _animationController.forward();

    // Check if user is already registered
    final userProvider = context.read<UserProvider>();
    if (userProvider.isLoggedIn) {
      // User exists, go to home
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/home');
        }
      });
    } else {
      // New user, start introduction process
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _startIntroduction();
        }
      });
    }
  }

  Future<void> _requestPermissions() async {
    try {
      // For web, we only need microphone and camera permissions
      // Storage permission is not supported on web
      final permissions = [
        Permission.microphone,
        Permission.camera,
      ];

      Map<Permission, PermissionStatus> statuses = await permissions.request();

      _permissionsGranted = statuses.values.every(
        (status) => status == PermissionStatus.granted,
      );

      // On web, permissions are often granted when actually needed
      // So we'll be more lenient and not block the app
      if (!_permissionsGranted) {
        print('Some permissions not granted, but continuing...');
        _permissionsGranted = true; // Allow app to continue
      }
    } catch (e) {
      print('Permission request error (likely web): $e');
      // On web, permission errors are common, so we'll continue
      _permissionsGranted = true;
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Permisos Necesarios'),
        content: const Text(
          'Esta aplicación necesita acceso al micrófono y cámara para funcionar correctamente. '
          'En el navegador web, estos permisos se solicitan automáticamente cuando uses las funciones.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Configuración'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _requestPermissions();
            },
            child: const Text('Reintentar'),
          ),
        ],
      ),
    );
  }

  void _startIntroduction() {
    setState(() {
      _isIntroducing = true;
    });

    // Speak welcome message
    final voiceProvider = context.read<VoiceProvider>();
    voiceProvider.speak(
      "¡Hola! Soy tu asistente de visión. Para comenzar, por favor dime tu nombre. "
      "Puedes decir: Mi nombre es, seguido de tu nombre."
    );
  }

  Future<void> _handleVoiceIntroduction() async {
    final voiceProvider = context.read<VoiceProvider>();
    final userProvider = context.read<UserProvider>();

    if (voiceProvider.isListening) {
      await voiceProvider.stopListening();

      if (voiceProvider.recognizedText.isNotEmpty) {
        // Process introduction
        // For now, we'll use a simple text input as fallback
        _showNameInputDialog();
      }
    } else {
      await voiceProvider.startListening();
    }
  }

  void _showNameInputDialog() {
    final TextEditingController nameController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('¿Cuál es tu nombre?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Por favor, escribe tu nombre para personalizar tu experiencia:',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Tu nombre',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                await _createUser(nameController.text.trim());
              }
            },
            child: const Text('Continuar'),
          ),
        ],
      ),
    );
  }

  Future<void> _createUser(String name) async {
    final userProvider = context.read<UserProvider>();
    final voiceProvider = context.read<VoiceProvider>();

    setState(() {
      _isIntroducing = true;
    });

    final success = await userProvider.introduceUserByName(name);

    if (success) {
      await voiceProvider.speak(
        "¡Encantado de conocerte, $name! Ahora puedes usar comandos como "
        "'detectar personas' o 'buscar objetos'. ¡Vamos a comenzar!"
      );

      Future.delayed(const Duration(seconds: 4), () {
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/home');
        }
      });
    } else {
      setState(() {
        _isIntroducing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error al crear usuario. Inténtalo de nuevo.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1976D2),
              Color(0xFF42A5F5),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo and Title
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        children: [
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(60),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.visibility,
                              size: 60,
                              color: Color(0xFF1976D2),
                            ),
                          ),
                          const SizedBox(height: 24),
                          const Text(
                            'Vision Assistant',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Tu asistente de visión inteligente',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 60),

                  // Status and Actions
                  Consumer2<UserProvider, VoiceProvider>(
                    builder: (context, userProvider, voiceProvider, child) {
                      if (userProvider.isLoading || !_permissionsGranted) {
                        return Column(
                          children: [
                            const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _permissionsGranted
                                ? 'Inicializando...'
                                : 'Solicitando permisos...',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        );
                      }

                      if (_isIntroducing) {
                        return Column(
                          children: [
                            GestureDetector(
                              onTap: _handleVoiceIntroduction,
                              child: Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: voiceProvider.isListening
                                      ? Colors.red
                                      : Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 15,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  voiceProvider.isListening
                                      ? Icons.mic
                                      : Icons.mic_none,
                                  size: 40,
                                  color: voiceProvider.isListening
                                      ? Colors.white
                                      : const Color(0xFF1976D2),
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                            Text(
                              voiceProvider.isListening
                                  ? 'Escuchando tu nombre...'
                                  : 'Toca para decir tu nombre',
                              style: const TextStyle(
                                fontSize: 18,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            TextButton(
                              onPressed: _showNameInputDialog,
                              child: const Text(
                                'Prefiero escribir mi nombre',
                                style: TextStyle(
                                  color: Colors.white70,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        );
                      }

                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
