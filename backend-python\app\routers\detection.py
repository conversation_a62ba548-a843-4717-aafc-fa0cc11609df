"""
Object/Person detection endpoints
"""
from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Dict, Any
import io
from PIL import Image

from app.services.detection_service import DetectionService

router = APIRouter()
detection_service = DetectionService()

@router.post("/detect/image")
async def detect_objects_in_image(file: UploadFile = File(...)):
    """
    Detect objects/persons in uploaded image
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        
        # Perform detection
        results = await detection_service.detect_objects(image)
        
        return {
            "success": True,
            "detections": results,
            "total_objects": len(results),
            "filename": file.filename
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Detection failed: {str(e)}")

@router.post("/detect/realtime")
async def detect_realtime(file: UploadFile = File(...)):
    """
    Real-time detection for mobile camera feed
    """
    try:
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        
        # Quick detection for real-time processing
        results = await detection_service.detect_objects(image, quick_mode=True)
        
        return {
            "success": True,
            "detections": results,
            "timestamp": detection_service.get_timestamp()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Real-time detection failed: {str(e)}")

@router.get("/detect/classes")
async def get_detection_classes():
    """
    Get available detection classes
    """
    return {
        "classes": detection_service.get_available_classes(),
        "total_classes": len(detection_service.get_available_classes())
    }
