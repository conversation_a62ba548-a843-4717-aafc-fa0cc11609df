"""
Object/Person detection endpoints
"""
from fastapi import APIRouter, File, UploadFile, HTTPException, Depends, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import io
import time
import uuid
from PIL import Image

from app.services.detection_service import DetectionService
from app.services.user_service import UserService
from app.models.database import get_db, DetectionSession
from app.models.schemas import DetectionResponse

router = APIRouter()
detection_service = DetectionService()
user_service = UserService()

@router.post("/detect/image", response_model=DetectionResponse)
async def detect_objects_in_image(
    file: UploadFile = File(...),
    user_id: str = Query(None, description="User ID for session tracking"),
    db: Session = Depends(get_db)
):
    """
    Detect objects/persons in uploaded image with user session tracking
    """
    start_time = time.time()
    session_id = str(uuid.uuid4())

    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Read and process image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # Perform detection
        results = await detection_service.detect_objects(image)
        processing_time = time.time() - start_time

        # Save detection session to database if user_id provided
        if user_id:
            try:
                db_session = DetectionSession(
                    session_id=session_id,
                    user_id=user_id,
                    persons_detected=results.get("person_count", 0),
                    total_objects=results.get("total_objects", 0),
                    detection_data=str(results),  # Convert to JSON string
                    image_path=file.filename,
                    processing_time=processing_time
                )
                db.add(db_session)
                db.commit()
            except Exception as e:
                # Don't fail the request if database save fails
                print(f"Failed to save detection session: {e}")

        return DetectionResponse(
            success=True,
            detections=results,
            session_id=session_id,
            user_id=user_id,
            processing_time=processing_time,
            filename=file.filename
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Detection failed: {str(e)}")

@router.post("/detect/realtime")
async def detect_realtime(file: UploadFile = File(...)):
    """
    Real-time detection for mobile camera feed
    """
    try:
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # Quick detection for real-time processing
        results = await detection_service.detect_objects(image, quick_mode=True)

        return {
            "success": True,
            "detections": results,
            "timestamp": detection_service.get_timestamp()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Real-time detection failed: {str(e)}")

@router.get("/detect/classes")
async def get_detection_classes():
    """
    Get available detection classes
    """
    return {
        "classes": detection_service.get_available_classes(),
        "total_classes": len(detection_service.get_available_classes())
    }
