import 'dart:io';
import 'package:dio/dio.dart';
import '../models/detection_result.dart';
import '../models/voice_command.dart';
import '../models/user.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8000/api/v1';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    ));
  }

  Future<DetectionResult> detectObjects(String imagePath, {String? userId}) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(imagePath),
      });

      Map<String, dynamic> queryParams = {};
      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      Response response = await _dio.post(
        '/detect/image',
        data: formData,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return DetectionResult.fromJson(response.data);
      } else {
        throw Exception('Failed to detect objects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error detecting objects: $e');
    }
  }

  Future<DetectionResult> detectRealtime(String imagePath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(imagePath),
      });

      Response response = await _dio.post('/detect/realtime', data: formData);

      if (response.statusCode == 200) {
        return DetectionResult.fromJson(response.data);
      } else {
        throw Exception('Failed to detect objects in realtime: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error in realtime detection: $e');
    }
  }

  Future<VoiceCommand> processVoiceCommand(String audioPath, {String? userId}) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(audioPath),
      });

      Map<String, dynamic> queryParams = {};
      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      Response response = await _dio.post(
        '/voice/command',
        data: formData,
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return VoiceCommand.fromJson(response.data);
      } else {
        throw Exception('Failed to process voice command: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error processing voice command: $e');
    }
  }

  Future<List<String>> getAvailableCommands() async {
    try {
      Response response = await _dio.get('/voice/commands');

      if (response.statusCode == 200) {
        return List<String>.from(response.data['commands'] ?? []);
      } else {
        throw Exception('Failed to get commands: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting commands: $e');
    }
  }

  Future<Map<String, dynamic>> getHealthStatus() async {
    try {
      Response response = await _dio.get('/health');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to get health status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting health status: $e');
    }
  }

  // User management methods
  Future<Map<String, dynamic>> introduceUser(String audioPath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(audioPath),
      });

      Response response = await _dio.post('/users/introduction', data: formData);

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to process introduction: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error processing introduction: $e');
    }
  }

  Future<Map<String, dynamic>?> createUser(Map<String, dynamic> userData) async {
    try {
      Response response = await _dio.post('/users/create', data: userData);

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to create user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error creating user: $e');
    }
  }

  Future<User?> getUser(String userId) async {
    try {
      Response response = await _dio.get('/users/$userId');

      if (response.statusCode == 200) {
        return User.fromJson(response.data);
      } else {
        throw Exception('Failed to get user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting user: $e');
    }
  }

  Future<Map<String, dynamic>?> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      Response response = await _dio.put('/users/$userId', data: updates);

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to update user: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating user: $e');
    }
  }

  Future<Map<String, dynamic>?> getUserStats(String userId) async {
    try {
      Response response = await _dio.get('/users/$userId/stats');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to get user stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting user stats: $e');
    }
  }

  Future<bool> updateVoiceProfile(String userId, String audioPath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(audioPath),
      });

      Response response = await _dio.post('/users/$userId/voice-profile', data: formData);

      return response.statusCode == 200;
    } catch (e) {
      throw Exception('Error updating voice profile: $e');
    }
  }
}
