import 'dart:io';
import 'package:dio/dio.dart';
import '../models/detection_result.dart';
import '../models/voice_command.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:8000/api/v1';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
    ));
  }

  Future<DetectionResult> detectObjects(String imagePath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(imagePath),
      });

      Response response = await _dio.post('/detect/image', data: formData);
      
      if (response.statusCode == 200) {
        return DetectionResult.fromJson(response.data);
      } else {
        throw Exception('Failed to detect objects: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error detecting objects: $e');
    }
  }

  Future<DetectionResult> detectRealtime(String imagePath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(imagePath),
      });

      Response response = await _dio.post('/detect/realtime', data: formData);
      
      if (response.statusCode == 200) {
        return DetectionResult.fromJson(response.data);
      } else {
        throw Exception('Failed to detect objects in realtime: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error in realtime detection: $e');
    }
  }

  Future<VoiceCommand> processVoiceCommand(String audioPath) async {
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(audioPath),
      });

      Response response = await _dio.post('/voice/command', data: formData);
      
      if (response.statusCode == 200) {
        return VoiceCommand.fromJson(response.data);
      } else {
        throw Exception('Failed to process voice command: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error processing voice command: $e');
    }
  }

  Future<List<String>> getAvailableCommands() async {
    try {
      Response response = await _dio.get('/voice/commands');
      
      if (response.statusCode == 200) {
        return List<String>.from(response.data['commands'] ?? []);
      } else {
        throw Exception('Failed to get commands: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting commands: $e');
    }
  }

  Future<Map<String, dynamic>> getHealthStatus() async {
    try {
      Response response = await _dio.get('/health');
      
      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to get health status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting health status: $e');
    }
  }
}
