"""
Voice recognition and command processing service
"""
import speech_recognition as sr
import whisper
import io
import tempfile
import os
from typing import Dict, Any, List, Optional
import logging
import re
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class VoiceService:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.whisper_model = None
        self.load_whisper_model()

        # Define voice commands in Spanish
        self.commands = {
            "detectar": "detect",
            "buscar": "search",
            "encontrar": "find",
            "contar": "count",
            "describir": "describe",
            "ayuda": "help",
            "parar": "stop",
            "repetir": "repeat",
            "nombre": "introduce",
            "llamo": "introduce",
            "soy": "introduce"
        }

        self.objects_spanish = {
            "persona": "person",
            "personas": "person",
            "gente": "person",
            "hombre": "person",
            "mujer": "person",
            "niño": "person",
            "niña": "person",
            "auto": "car",
            "carro": "car",
            "coche": "car",
            "bicicleta": "bicycle",
            "perro": "dog",
            "gato": "cat"
        }

    def load_whisper_model(self):
        """Load Whisper model for Spanish speech recognition"""
        try:
            self.whisper_model = whisper.load_model("base")
            logger.info("Whisper model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            self.whisper_model = None

    async def recognize_speech(self, audio_data: bytes) -> str:
        """
        Recognize speech from audio data
        """
        try:
            # Save audio data to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            try:
                # Use Whisper for Spanish recognition
                if self.whisper_model:
                    result = self.whisper_model.transcribe(temp_file_path, language='es')
                    text = result["text"].strip()
                else:
                    # Fallback to speech_recognition
                    with sr.AudioFile(temp_file_path) as source:
                        audio = self.recognizer.record(source)
                    text = self.recognizer.recognize_google(audio, language='es-ES')

                logger.info(f"Recognized text: {text}")
                return text.lower()

            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Speech recognition failed: {e}")
            raise Exception(f"Speech recognition failed: {e}")

    async def process_voice_command(self, text: str) -> Dict[str, Any]:
        """
        Process general voice command
        """
        text = text.lower().strip()

        # Extract command and parameters
        command = None
        target_object = None

        # Find command
        for spanish_cmd, english_cmd in self.commands.items():
            if spanish_cmd in text:
                command = english_cmd
                break

        # Find target object
        for spanish_obj, english_obj in self.objects_spanish.items():
            if spanish_obj in text:
                target_object = english_obj
                break

        return {
            "command": command,
            "target_object": target_object,
            "original_text": text,
            "understood": command is not None
        }

    async def process_vision_command(self, text: str) -> Dict[str, Any]:
        """
        Process vision-specific commands
        """
        command_result = await self.process_voice_command(text)

        if not command_result["understood"]:
            return {
                "action": "unknown",
                "response": "No entendí el comando. Intenta con: 'detectar personas' o 'buscar objetos'",
                "parameters": {}
            }

        command = command_result["command"]
        target = command_result["target_object"]

        # Generate appropriate response based on command
        if command == "detect":
            if target == "person":
                return {
                    "action": "detect_persons",
                    "response": "Detectando personas en la imagen...",
                    "parameters": {"filter": "person"}
                }
            else:
                return {
                    "action": "detect_all",
                    "response": "Detectando todos los objetos en la imagen...",
                    "parameters": {"filter": "all"}
                }

        elif command == "count":
            return {
                "action": "count_objects",
                "response": f"Contando {target or 'objetos'} en la imagen...",
                "parameters": {"target": target or "all"}
            }

        elif command == "help":
            return {
                "action": "help",
                "response": "Comandos disponibles: detectar personas, buscar objetos, contar elementos",
                "parameters": {}
            }

        else:
            return {
                "action": "general",
                "response": f"Procesando comando: {command}",
                "parameters": {"command": command, "target": target}
            }

    def get_available_commands(self) -> List[str]:
        """Get list of available commands in Spanish"""
        return list(self.commands.keys())

    def get_command_examples(self) -> List[str]:
        """Get example commands"""
        return [
            "detectar personas",
            "buscar objetos",
            "contar personas",
            "describir imagen",
            "ayuda"
        ]

    async def process_introduction_command(self, text: str) -> Dict[str, Any]:
        """
        Process user introduction command
        """
        text = text.lower().strip()

        # Check if this is an introduction
        intro_keywords = ["nombre", "llamo", "soy"]
        if not any(keyword in text for keyword in intro_keywords):
            return {
                "action": "request_introduction",
                "response": "Hola! Para comenzar, por favor dime tu nombre. Puedes decir 'Mi nombre es...' o 'Me llamo...'",
                "parameters": {"needs_introduction": True}
            }

        # Extract name from text
        name = self.extract_name_from_introduction(text)
        if not name:
            return {
                "action": "request_introduction_retry",
                "response": "No pude entender tu nombre claramente. Por favor, di lentamente: 'Mi nombre es' seguido de tu nombre.",
                "parameters": {"needs_introduction": True}
            }

        return {
            "action": "introduction_received",
            "response": f"Encantado de conocerte, {name}. Ahora puedes usar comandos como 'detectar personas' o 'buscar objetos'.",
            "parameters": {"extracted_name": name, "needs_introduction": False}
        }

    def extract_name_from_introduction(self, text: str) -> Optional[str]:
        """
        Extract name from introduction text
        """
        text = text.lower().strip()

        # Common patterns for name introduction in Spanish
        patterns = [
            "mi nombre es ",
            "me llamo ",
            "soy ",
            "mi nombre ",
            "llamo ",
            "nombre es "
        ]

        for pattern in patterns:
            if pattern in text:
                # Extract everything after the pattern
                name_part = text.split(pattern, 1)[1].strip()
                # Take only the first few words (usually just first name)
                name_words = name_part.split()[:2]  # Max 2 words for name
                if name_words:
                    return " ".join(name_words).title()

        # If no pattern found, try to extract from the whole text
        words = text.split()
        if len(words) >= 2 and any(word in text for word in ["nombre", "llamo", "soy"]):
            # Look for the word after these keywords
            for i, word in enumerate(words):
                if word in ["nombre", "llamo", "soy"] and i + 1 < len(words):
                    return words[i + 1].title()

        return None

    def generate_personalized_response(self, user_name: str, command_result: Dict[str, Any]) -> str:
        """
        Generate personalized response including user's name
        """
        base_response = command_result.get("response", "")
        action = command_result.get("action", "")

        if action == "detect_persons":
            return f"{user_name}, {base_response}"
        elif action == "help":
            return f"Hola {user_name}, {base_response}"
        else:
            return base_response

    def get_welcome_commands(self) -> List[str]:
        """Get commands for new users"""
        return [
            "Para empezar, di tu nombre: 'Mi nombre es [tu nombre]'",
            "Luego puedes usar: 'detectar personas'",
            "O también: 'buscar objetos'",
            "Si necesitas ayuda: 'ayuda'"
        ]
