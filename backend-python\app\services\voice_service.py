"""
Voice recognition and command processing service
"""
import speech_recognition as sr
import whisper
import io
import tempfile
import os
from typing import Dict, Any, List
import logging
import re

logger = logging.getLogger(__name__)

class VoiceService:
    def __init__(self):
        self.recognizer = sr.Recognizer()
        self.whisper_model = None
        self.load_whisper_model()
        
        # Define voice commands in Spanish
        self.commands = {
            "detectar": "detect",
            "buscar": "search", 
            "encontrar": "find",
            "contar": "count",
            "describir": "describe",
            "ayuda": "help",
            "parar": "stop",
            "repetir": "repeat"
        }
        
        self.objects_spanish = {
            "persona": "person",
            "personas": "person",
            "gente": "person",
            "hombre": "person",
            "mujer": "person",
            "niño": "person",
            "niña": "person",
            "auto": "car",
            "carro": "car",
            "coche": "car",
            "bicicleta": "bicycle",
            "perro": "dog",
            "gato": "cat"
        }
    
    def load_whisper_model(self):
        """Load Whisper model for Spanish speech recognition"""
        try:
            self.whisper_model = whisper.load_model("base")
            logger.info("Whisper model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            self.whisper_model = None
    
    async def recognize_speech(self, audio_data: bytes) -> str:
        """
        Recognize speech from audio data
        """
        try:
            # Save audio data to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                # Use Whisper for Spanish recognition
                if self.whisper_model:
                    result = self.whisper_model.transcribe(temp_file_path, language='es')
                    text = result["text"].strip()
                else:
                    # Fallback to speech_recognition
                    with sr.AudioFile(temp_file_path) as source:
                        audio = self.recognizer.record(source)
                    text = self.recognizer.recognize_google(audio, language='es-ES')
                
                logger.info(f"Recognized text: {text}")
                return text.lower()
                
            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Speech recognition failed: {e}")
            raise Exception(f"Speech recognition failed: {e}")
    
    async def process_voice_command(self, text: str) -> Dict[str, Any]:
        """
        Process general voice command
        """
        text = text.lower().strip()
        
        # Extract command and parameters
        command = None
        target_object = None
        
        # Find command
        for spanish_cmd, english_cmd in self.commands.items():
            if spanish_cmd in text:
                command = english_cmd
                break
        
        # Find target object
        for spanish_obj, english_obj in self.objects_spanish.items():
            if spanish_obj in text:
                target_object = english_obj
                break
        
        return {
            "command": command,
            "target_object": target_object,
            "original_text": text,
            "understood": command is not None
        }
    
    async def process_vision_command(self, text: str) -> Dict[str, Any]:
        """
        Process vision-specific commands
        """
        command_result = await self.process_voice_command(text)
        
        if not command_result["understood"]:
            return {
                "action": "unknown",
                "response": "No entendí el comando. Intenta con: 'detectar personas' o 'buscar objetos'",
                "parameters": {}
            }
        
        command = command_result["command"]
        target = command_result["target_object"]
        
        # Generate appropriate response based on command
        if command == "detect":
            if target == "person":
                return {
                    "action": "detect_persons",
                    "response": "Detectando personas en la imagen...",
                    "parameters": {"filter": "person"}
                }
            else:
                return {
                    "action": "detect_all",
                    "response": "Detectando todos los objetos en la imagen...",
                    "parameters": {"filter": "all"}
                }
        
        elif command == "count":
            return {
                "action": "count_objects",
                "response": f"Contando {target or 'objetos'} en la imagen...",
                "parameters": {"target": target or "all"}
            }
        
        elif command == "help":
            return {
                "action": "help",
                "response": "Comandos disponibles: detectar personas, buscar objetos, contar elementos",
                "parameters": {}
            }
        
        else:
            return {
                "action": "general",
                "response": f"Procesando comando: {command}",
                "parameters": {"command": command, "target": target}
            }
    
    def get_available_commands(self) -> List[str]:
        """Get list of available commands in Spanish"""
        return list(self.commands.keys())
    
    def get_command_examples(self) -> List[str]:
        """Get example commands"""
        return [
            "detectar personas",
            "buscar objetos",
            "contar personas",
            "describir imagen",
            "ayuda"
        ]
