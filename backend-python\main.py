"""
Main FastAPI application for Vision Assistant API
"""
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from dotenv import load_dotenv

from app.routers import detection, voice, health
from app.core.config import settings

# Load environment variables
load_dotenv()

# Create FastAPI instance
app = FastAPI(
    title="Vision Assistant API",
    description="API para aplicación móvil de asistencia visual con comandos de voz",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En producción, especificar dominios exactos
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(detection.router, prefix="/api/v1", tags=["detection"])
app.include_router(voice.router, prefix="/api/v1", tags=["voice"])

@app.get("/")
async def root():
    return {
        "message": "Vision Assistant API",
        "version": "1.0.0",
        "docs": "/docs"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG
    )
