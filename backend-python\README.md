# Vision Assistant - Backend Python

API REST desarrollada con FastAPI para el procesamiento de comandos de voz y detección de objetos usando YOLO.

## Características

- **API REST con FastAPI**: Endpoints para detección y procesamiento de voz
- **Detección con YOLOv8**: Reconocimiento de personas y objetos en tiempo real
- **Reconocimiento de voz**: Procesamiento de comandos en español usando Whisper
- **Procesamiento de imágenes**: OpenCV para manipulación de imágenes
- **Documentación automática**: Swagger UI disponible en `/docs`

## Estructura del Proyecto

```
backend-python/
├── app/
│   ├── core/
│   │   └── config.py          # Configuración de la aplicación
│   ├── models/                # Modelos de datos (futuro)
│   ├── routers/               # Endpoints de la API
│   │   ├── detection.py       # Endpoints de detección
│   │   ├── voice.py          # Endpoints de voz
│   │   └── health.py         # Health checks
│   ├── services/              # Lógica de negocio
│   │   ├── detection_service.py  # Servicio de detección YOLO
│   │   └── voice_service.py      # Servicio de procesamiento de voz
│   └── utils/                 # Utilidades
├── models/                    # Modelos entrenados
├── uploads/                   # Archivos temporales
├── requirements.txt           # Dependencias
├── .env.example              # Variables de entorno ejemplo
└── main.py                   # Punto de entrada
```

## Instalación

### Prerrequisitos

- Python 3.8 o superior
- pip (gestor de paquetes de Python)
- Espacio en disco para modelos YOLO (~50MB)

### Pasos de instalación

1. **Crear entorno virtual**:
```bash
python -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate
```

2. **Instalar dependencias**:
```bash
pip install -r requirements.txt
```

3. **Configurar variables de entorno**:
```bash
cp .env.example .env
# Editar .env con tu configuración
```

4. **Crear directorios necesarios**:
```bash
mkdir -p uploads models
```

## Configuración

### Variables de Entorno (.env)

```env
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/vision_app_db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External APIs
OPENAI_API_KEY=your-openai-api-key-here

# File Storage
UPLOAD_FOLDER=uploads/
MAX_FILE_SIZE=10485760  # 10MB

# Model Configuration
YOLO_MODEL_PATH=models/yolov8n.pt
CONFIDENCE_THRESHOLD=0.5
```

## Ejecución

### Desarrollo
```bash
python main.py
```

### Producción
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

La API estará disponible en:
- **API**: http://localhost:8000
- **Documentación**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Endpoints de la API

### Health Check
- `GET /api/v1/health` - Estado básico del servicio
- `GET /api/v1/health/detailed` - Estado detallado con componentes

### Detección de Objetos
- `POST /api/v1/detect/image` - Detectar objetos en imagen
- `POST /api/v1/detect/realtime` - Detección en tiempo real
- `GET /api/v1/detect/classes` - Obtener clases disponibles

### Procesamiento de Voz
- `POST /api/v1/voice/recognize` - Reconocer texto de audio
- `POST /api/v1/voice/command` - Procesar comando de voz
- `GET /api/v1/voice/commands` - Obtener comandos disponibles

## Uso de la API

### Detección de Objetos

```python
import requests

# Detectar objetos en imagen
with open('imagen.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/api/v1/detect/image', files=files)
    result = response.json()
    print(f"Personas detectadas: {result['detections']['person_count']}")
```

### Procesamiento de Voz

```python
import requests

# Procesar comando de voz
with open('audio.wav', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/api/v1/voice/command', files=files)
    result = response.json()
    print(f"Comando reconocido: {result['recognized_text']}")
    print(f"Acción: {result['action']}")
```

## Modelos de Machine Learning

### YOLO (You Only Look Once)

El sistema utiliza YOLOv8 para detección de objetos:

- **Modelo por defecto**: `yolov8n.pt` (nano, más rápido)
- **Alternativas**: `yolov8s.pt`, `yolov8m.pt`, `yolov8l.pt`, `yolov8x.pt`
- **Clases detectadas**: 80 clases del dataset COCO
- **Enfoque especial**: Detección de personas (clase 0)

### Whisper (OpenAI)

Para reconocimiento de voz en español:

- **Modelo**: `base` (equilibrio entre velocidad y precisión)
- **Idioma**: Español (es)
- **Formato de audio**: WAV, MP3, M4A, etc.

## Comandos de Voz Soportados

### Comandos Básicos
- **"detectar"** / **"buscar"** / **"encontrar"** - Iniciar detección
- **"contar"** - Contar objetos específicos
- **"describir"** - Describir la imagen
- **"ayuda"** - Mostrar ayuda
- **"parar"** - Detener operación

### Objetos Reconocidos en Español
- **"persona"** / **"personas"** / **"gente"**
- **"auto"** / **"carro"** / **"coche"**
- **"bicicleta"**
- **"perro"** / **"gato"**

### Ejemplos de Comandos
- "detectar personas"
- "buscar objetos"
- "contar personas"
- "describir imagen"

## Desarrollo

### Agregar Nuevos Endpoints

1. Crear archivo en `app/routers/`
2. Definir endpoints con FastAPI
3. Importar en `main.py`

```python
# app/routers/nuevo_endpoint.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/nuevo")
async def nuevo_endpoint():
    return {"message": "Nuevo endpoint"}
```

### Agregar Nuevos Servicios

1. Crear archivo en `app/services/`
2. Implementar lógica de negocio
3. Usar en routers

```python
# app/services/nuevo_service.py
class NuevoService:
    def procesar(self, data):
        # Lógica de procesamiento
        return resultado
```

### Testing

```bash
# Instalar dependencias de testing
pip install pytest pytest-asyncio httpx

# Ejecutar tests
pytest
```

## Optimización y Rendimiento

### Para Producción

1. **Usar modelo YOLO optimizado**:
   - Convertir a TensorRT para NVIDIA GPUs
   - Usar ONNX para compatibilidad multiplataforma

2. **Configurar workers**:
```bash
uvicorn main:app --workers 4 --host 0.0.0.0 --port 8000
```

3. **Usar proxy reverso** (Nginx):
```nginx
location / {
    proxy_pass http://127.0.0.1:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### Monitoreo

- Logs automáticos con FastAPI
- Métricas de rendimiento en `/health/detailed`
- Monitoreo de uso de memoria y CPU

## Problemas Conocidos

1. **Primer arranque lento**: YOLO descarga el modelo automáticamente
2. **Uso de memoria**: Modelos grandes requieren más RAM
3. **Dependencias de sistema**: OpenCV puede requerir librerías adicionales

## Solución de Problemas

### Error de instalación de OpenCV
```bash
# Ubuntu/Debian
sudo apt-get install python3-opencv

# macOS
brew install opencv

# Windows
pip install opencv-python-headless
```

### Error de permisos de archivos
```bash
chmod +x main.py
mkdir -p uploads models
```

### Error de modelo YOLO no encontrado
El modelo se descarga automáticamente en el primer uso. Asegurar conexión a internet.

## Contribución

1. Fork el repositorio
2. Crear rama para feature: `git checkout -b feature/nueva-funcionalidad`
3. Commit cambios: `git commit -am 'Agregar nueva funcionalidad'`
4. Push a la rama: `git push origin feature/nueva-funcionalidad`
5. Crear Pull Request

## Licencia

Proyecto académico - Consultar con el autor para uso comercial.
