import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class UserProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  bool _isFirstTime = true;
  
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isFirstTime => _isFirstTime;
  bool get isLoggedIn => _currentUser != null;
  
  Future<void> initializeUser() async {
    _isLoading = true;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      final isFirstTime = prefs.getBool('is_first_time') ?? true;
      
      _isFirstTime = isFirstTime;
      
      if (userId != null) {
        // Get user from API
        _currentUser = await _apiService.getUser(userId);
        if (_currentUser != null) {
          _isFirstTime = false;
        }
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<bool> introduceUser(String audioPath) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final result = await _apiService.introduceUser(audioPath);
      
      if (result['user_id'] != null) {
        // Create user object
        _currentUser = User(
          userId: result['user_id'],
          name: result['user_name'] ?? 'Usuario',
          preferredLanguage: 'es-ES',
          voiceSpeed: 0.8,
          confidenceThreshold: 0.5,
          createdAt: DateTime.now(),
          lastSeen: DateTime.now(),
          isActive: true,
        );
        
        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_id', _currentUser!.userId);
        await prefs.setString('user_name', _currentUser!.name);
        await prefs.setBool('is_first_time', false);
        
        _isFirstTime = false;
        
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<bool> createUser(String name) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final userData = {
        'name': name,
        'preferred_language': 'es-ES',
        'voice_speed': 0.8,
      };
      
      final result = await _apiService.createUser(userData);
      
      if (result != null) {
        _currentUser = User.fromJson(result);
        
        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_id', _currentUser!.userId);
        await prefs.setString('user_name', _currentUser!.name);
        await prefs.setBool('is_first_time', false);
        
        _isFirstTime = false;
        
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<void> updateUser(Map<String, dynamic> updates) async {
    if (_currentUser == null) return;
    
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      final result = await _apiService.updateUser(_currentUser!.userId, updates);
      
      if (result != null) {
        _currentUser = User.fromJson(result);
        
        // Update local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_name', _currentUser!.name);
        
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  Future<Map<String, dynamic>?> getUserStats() async {
    if (_currentUser == null) return null;
    
    try {
      return await _apiService.getUserStats(_currentUser!.userId);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }
  
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
    await prefs.remove('user_name');
    await prefs.setBool('is_first_time', true);
    
    _currentUser = null;
    _isFirstTime = true;
    _error = null;
    
    notifyListeners();
  }
  
  void clearError() {
    _error = null;
    notifyListeners();
  }
  
  String getWelcomeMessage() {
    if (_currentUser == null) {
      return "¡Hola! Soy tu asistente de visión. Para comenzar, por favor dime tu nombre.";
    }
    
    return "¡Hola ${_currentUser!.name}! ¿En qué puedo ayudarte hoy?";
  }
  
  List<String> getSuggestedCommands() {
    if (_isFirstTime) {
      return [
        "Di tu nombre: 'Mi nombre es [tu nombre]'",
        "Luego usa: 'detectar personas'",
        "O también: 'buscar objetos'",
        "Para ayuda: 'ayuda'"
      ];
    }
    
    return [
      "detectar personas",
      "buscar objetos",
      "contar personas",
      "describir imagen",
      "ayuda"
    ];
  }
}
