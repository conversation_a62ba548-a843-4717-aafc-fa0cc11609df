import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/detection_provider.dart';
import '../providers/voice_provider.dart';
import '../providers/user_provider.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _announceInstructions();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        _controller = CameraController(
          _cameras![0],
          ResolutionPreset.medium,
        );
        await _controller!.initialize();
        setState(() {
          _isCameraInitialized = true;
        });

        // Auto-take photo after camera initializes
        Future.delayed(const Duration(seconds: 2), () {
          _takePicture();
        });
      }
    } catch (e) {
      print('Error initializing camera: $e');
      // For web, we'll simulate the detection
      _simulateWebDetection();
    }
  }

  Future<void> _announceInstructions() async {
    final voiceProvider = context.read<VoiceProvider>();
    await voiceProvider.speak(
      "Cámara activada. Procesando imagen automáticamente en 2 segundos..."
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) return;

    try {
      final XFile picture = await _controller!.takePicture();
      await _processImage(picture.path);
    } catch (e) {
      print('Error taking picture: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        await _processImage(image.path);
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  Future<void> _processImage(String imagePath) async {
    final detectionProvider = context.read<DetectionProvider>();
    final userProvider = context.read<UserProvider>();

    // Get user ID for session tracking
    final userId = userProvider.currentUser?.userId;

    await detectionProvider.detectObjects(imagePath, userId: userId);

    // Announce results via voice
    final voiceProvider = context.read<VoiceProvider>();
    final detection = detectionProvider.lastDetection;

    if (detection != null) {
      String announcement = '';
      if (detection.personCount > 0) {
        announcement = 'Detecté ${detection.personCount} persona${detection.personCount > 1 ? 's' : ''}';
      } else {
        announcement = 'No se detectaron personas';
      }

      if (detection.totalObjects > detection.personCount) {
        int otherObjects = detection.totalObjects - detection.personCount;
        announcement += ' y $otherObjects objeto${otherObjects > 1 ? 's' : ''} más';
      }

      // Personalize announcement with user name
      if (userProvider.currentUser != null) {
        announcement = '${userProvider.currentUser!.name}, $announcement';
      }

      await voiceProvider.speak(announcement);
    }
  }

  Future<void> _simulateWebDetection() async {
    final voiceProvider = context.read<VoiceProvider>();
    final userProvider = context.read<UserProvider>();

    await voiceProvider.speak("Procesando imagen de la cámara web...");

    // Simulate processing time
    await Future.delayed(const Duration(seconds: 2));

    // Mock detection results
    final userName = userProvider.currentUser?.name ?? "Usuario";
    await voiceProvider.speak(
      "$userName, he detectado 1 persona en la imagen. "
      "También veo 2 objetos más en la escena. "
      "Presiona el botón de regresar para volver al menú principal."
    );

    // Auto-return to home after announcement
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cámara'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        child: SizedBox(
          height: MediaQuery.of(context).size.height - AppBar().preferredSize.height - MediaQuery.of(context).padding.top,
          child: Column(
            children: [
              // Camera Preview
              SizedBox(
                height: 300,
                child: _isCameraInitialized && _controller != null
                    ? CameraPreview(_controller!)
                    : const Center(
                        child: CircularProgressIndicator(),
                      ),
              ),

              // Detection Results
              Expanded(
                child: Consumer<DetectionProvider>(
                  builder: (context, detectionProvider, child) {
                if (detectionProvider.isDetecting) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Detectando objetos...'),
                      ],
                    ),
                  );
                }

                if (detectionProvider.error != null) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Error: ${detectionProvider.error}',
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: detectionProvider.clearError,
                            child: const Text('Reintentar'),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                final detection = detectionProvider.lastDetection;
                if (detection != null) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildResultCard(
                              'Personas',
                              detection.personCount.toString(),
                              Icons.person,
                              Colors.blue,
                            ),
                            _buildResultCard(
                              'Total Objetos',
                              detection.totalObjects.toString(),
                              Icons.category,
                              Colors.green,
                            ),
                          ],
                        ),
                        if (detection.persons.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          const Text(
                            'Personas detectadas:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Expanded(
                            child: ListView.builder(
                              itemCount: detection.persons.length,
                              itemBuilder: (context, index) {
                                final person = detection.persons[index];
                                return ListTile(
                                  leading: const Icon(Icons.person),
                                  title: Text('Persona ${index + 1}'),
                                  subtitle: Text(
                                    'Confianza: ${(person.confidence * 100).toStringAsFixed(1)}%',
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                }

                return const Center(
                  child: Text(
                    'Toma una foto para detectar objetos',
                    style: TextStyle(fontSize: 16),
                  ),
                );
                  },
                ),
              ),

              // Control Buttons - Simplified for accessibility
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Status text
                    const Text(
                      'Procesando automáticamente...',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    // Back button
                    ElevatedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Regresar'),
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(200, 50),
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
  }

  Widget _buildResultCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
