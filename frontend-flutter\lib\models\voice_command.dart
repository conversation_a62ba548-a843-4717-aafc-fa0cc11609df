class VoiceCommand {
  final String recognizedText;
  final String action;
  final String? response;
  final Map<String, dynamic> parameters;

  VoiceCommand({
    required this.recognizedText,
    required this.action,
    this.response,
    required this.parameters,
  });

  factory VoiceCommand.fromJson(Map<String, dynamic> json) {
    return VoiceCommand(
      recognizedText: json['recognized_text'] ?? '',
      action: json['action'] ?? '',
      response: json['response'],
      parameters: json['parameters'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recognized_text': recognizedText,
      'action': action,
      'response': response,
      'parameters': parameters,
    };
  }
}
