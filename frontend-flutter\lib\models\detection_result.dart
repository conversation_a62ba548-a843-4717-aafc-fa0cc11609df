class DetectionResult {
  final List<DetectedObject> allObjects;
  final List<DetectedObject> persons;
  final int personCount;
  final int totalObjects;
  final String? filename;

  DetectionResult({
    required this.allObjects,
    required this.persons,
    required this.personCount,
    required this.totalObjects,
    this.filename,
  });

  factory DetectionResult.fromJson(Map<String, dynamic> json) {
    var detectionsData = json['detections'] ?? {};
    
    return DetectionResult(
      allObjects: (detectionsData['all_objects'] as List<dynamic>?)
          ?.map((obj) => DetectedObject.fromJson(obj))
          .toList() ?? [],
      persons: (detectionsData['persons'] as List<dynamic>?)
          ?.map((obj) => DetectedObject.fromJson(obj))
          .toList() ?? [],
      personCount: detectionsData['person_count'] ?? 0,
      totalObjects: detectionsData['total_objects'] ?? 0,
      filename: json['filename'],
    );
  }
}

class DetectedObject {
  final String className;
  final int classId;
  final double confidence;
  final BoundingBox bbox;
  final Point center;

  DetectedObject({
    required this.className,
    required this.classId,
    required this.confidence,
    required this.bbox,
    required this.center,
  });

  factory DetectedObject.fromJson(Map<String, dynamic> json) {
    return DetectedObject(
      className: json['class_name'] ?? '',
      classId: json['class_id'] ?? 0,
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      bbox: BoundingBox.fromJson(json['bbox'] ?? {}),
      center: Point.fromJson(json['center'] ?? {}),
    );
  }
}

class BoundingBox {
  final double x1;
  final double y1;
  final double x2;
  final double y2;

  BoundingBox({
    required this.x1,
    required this.y1,
    required this.x2,
    required this.y2,
  });

  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      x1: (json['x1'] ?? 0.0).toDouble(),
      y1: (json['y1'] ?? 0.0).toDouble(),
      x2: (json['x2'] ?? 0.0).toDouble(),
      y2: (json['y2'] ?? 0.0).toDouble(),
    );
  }
}

class Point {
  final double x;
  final double y;

  Point({
    required this.x,
    required this.y,
  });

  factory Point.fromJson(Map<String, dynamic> json) {
    return Point(
      x: (json['x'] ?? 0.0).toDouble(),
      y: (json['y'] ?? 0.0).toDouble(),
    );
  }
}
