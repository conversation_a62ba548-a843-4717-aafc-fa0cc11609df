"""
Pydantic schemas for API request/response models
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# User schemas
class UserCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="User's name")
    preferred_language: Optional[str] = Field("es-ES", description="Preferred language code")
    voice_speed: Optional[float] = Field(0.8, ge=0.1, le=2.0, description="Voice speed preference")

class UserResponse(BaseModel):
    user_id: str
    name: str
    preferred_language: str
    voice_speed: float
    confidence_threshold: float
    created_at: datetime
    last_seen: datetime
    is_active: bool

    class Config:
        from_attributes = True

class UserUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    preferred_language: Optional[str] = None
    voice_speed: Optional[float] = Field(None, ge=0.1, le=2.0)
    confidence_threshold: Optional[float] = Field(None, ge=0.1, le=1.0)

# Detection schemas
class BoundingBox(BaseModel):
    x1: float
    y1: float
    x2: float
    y2: float

class Point(BaseModel):
    x: float
    y: float

class DetectedObject(BaseModel):
    class_name: str
    class_id: int
    confidence: float
    bbox: BoundingBox
    center: Point

class DetectionResult(BaseModel):
    all_objects: List[DetectedObject]
    persons: List[DetectedObject]
    person_count: int
    total_objects: int

class DetectionResponse(BaseModel):
    success: bool
    detections: DetectionResult
    session_id: str
    user_id: Optional[str] = None
    processing_time: Optional[float] = None
    filename: Optional[str] = None

# Voice command schemas
class VoiceCommandRequest(BaseModel):
    user_id: Optional[str] = None
    language: Optional[str] = "es-ES"

class VoiceCommandResponse(BaseModel):
    success: bool
    recognized_text: str
    command_type: str
    action: str
    response: str
    parameters: Dict[str, Any]
    confidence_score: Optional[float] = None
    command_id: str
    user_id: Optional[str] = None
    processing_time: Optional[float] = None

# Welcome/Introduction schemas
class WelcomeResponse(BaseModel):
    message: str
    is_new_user: bool
    user_id: str
    suggested_commands: List[str]

class IntroductionRequest(BaseModel):
    recognized_name: str
    voice_characteristics: Optional[Dict[str, Any]] = None

# Session schemas
class SessionCreate(BaseModel):
    user_id: str
    detection_data: Optional[Dict[str, Any]] = None
    persons_detected: int = 0
    total_objects: int = 0
    image_path: Optional[str] = None

class SessionResponse(BaseModel):
    session_id: str
    user_id: str
    persons_detected: int
    total_objects: int
    processing_time: Optional[float]
    created_at: datetime

    class Config:
        from_attributes = True

# Health check schemas
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    service: str
    version: Optional[str] = None
    components: Optional[Dict[str, str]] = None

# Error schemas
class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None
    error_code: Optional[str] = None
