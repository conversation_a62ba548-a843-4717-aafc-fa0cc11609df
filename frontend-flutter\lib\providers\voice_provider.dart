import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../models/voice_command.dart';
import '../services/api_service.dart';

class VoiceProvider with ChangeNotifier {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final ApiService _apiService = ApiService();

  bool _isListening = false;
  bool _speechEnabled = false;
  String _recognizedText = '';
  VoiceCommand? _lastCommand;
  String? _error;

  bool get isListening => _isListening;
  bool get speechEnabled => _speechEnabled;
  String get recognizedText => _recognizedText;
  VoiceCommand? get lastCommand => _lastCommand;
  String? get error => _error;

  Future<void> initializeSpeech() async {
    _speechEnabled = await _speechToText.initialize();

    // Configure TTS for Spanish
    await _flutterTts.setLanguage('es-ES');
    await _flutterTts.setSpeechRate(0.8);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    notifyListeners();
  }

  Future<void> startListening() async {
    if (!_speechEnabled) return;

    _isListening = true;
    _error = null;
    notifyListeners();

    await _speechToText.listen(
      onResult: (result) {
        _recognizedText = result.recognizedWords;
        notifyListeners();
      },
      localeId: 'es_ES',
    );
  }

  Future<void> stopListening() async {
    _isListening = false;
    await _speechToText.stop();

    if (_recognizedText.isNotEmpty) {
      await processVoiceCommand(_recognizedText);
    }

    notifyListeners();
  }

  Future<void> processVoiceCommand(String text, {String? userId}) async {
    try {
      _lastCommand = await _apiService.processVoiceCommand(text, userId: userId);

      // Speak the response
      if (_lastCommand?.response != null) {
        await speak(_lastCommand!.response!);
      }

    } catch (e) {
      _error = e.toString();
    }

    notifyListeners();
  }

  Future<void> speak(String text) async {
    await _flutterTts.speak(text);
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearRecognizedText() {
    _recognizedText = '';
    notifyListeners();
  }
}
