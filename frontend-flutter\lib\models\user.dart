class User {
  final String userId;
  final String name;
  final String preferredLanguage;
  final double voiceSpeed;
  final double confidenceThreshold;
  final DateTime createdAt;
  final DateTime lastSeen;
  final bool isActive;

  User({
    required this.userId,
    required this.name,
    required this.preferredLanguage,
    required this.voiceSpeed,
    required this.confidenceThreshold,
    required this.createdAt,
    required this.lastSeen,
    required this.isActive,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['user_id'] ?? '',
      name: json['name'] ?? '',
      preferredLanguage: json['preferred_language'] ?? 'es-ES',
      voiceSpeed: (json['voice_speed'] ?? 0.8).toDouble(),
      confidenceThreshold: (json['confidence_threshold'] ?? 0.5).toDouble(),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      lastSeen: DateTime.parse(json['last_seen'] ?? DateTime.now().toIso8601String()),
      isActive: json['is_active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'name': name,
      'preferred_language': preferredLanguage,
      'voice_speed': voiceSpeed,
      'confidence_threshold': confidenceThreshold,
      'created_at': createdAt.toIso8601String(),
      'last_seen': lastSeen.toIso8601String(),
      'is_active': isActive,
    };
  }

  User copyWith({
    String? userId,
    String? name,
    String? preferredLanguage,
    double? voiceSpeed,
    double? confidenceThreshold,
    DateTime? createdAt,
    DateTime? lastSeen,
    bool? isActive,
  }) {
    return User(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      voiceSpeed: voiceSpeed ?? this.voiceSpeed,
      confidenceThreshold: confidenceThreshold ?? this.confidenceThreshold,
      createdAt: createdAt ?? this.createdAt,
      lastSeen: lastSeen ?? this.lastSeen,
      isActive: isActive ?? this.isActive,
    );
  }
}
