import 'package:flutter/foundation.dart';
import '../models/detection_result.dart';
import '../services/api_service.dart';

class DetectionProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  DetectionResult? _lastDetection;
  bool _isDetecting = false;
  String? _error;

  DetectionResult? get lastDetection => _lastDetection;
  bool get isDetecting => _isDetecting;
  String? get error => _error;

  Future<void> detectObjects(String imagePath, {String? userId}) async {
    _isDetecting = true;
    _error = null;
    notifyListeners();

    try {
      // For web, we'll simulate detection since file upload is complex
      await _simulateWebDetection(userId: userId);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isDetecting = false;
      notifyListeners();
    }
  }

  Future<void> _simulateWebDetection({String? userId}) async {
    // Simulate processing time
    await Future.delayed(const Duration(seconds: 2));

    // Create mock detection result
    _lastDetection = Detection(
      detectionId: 'web_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      imagePath: 'web_camera',
      personCount: 1,
      totalObjects: 3,
      persons: [
        DetectedPerson(
          personId: 'person_1',
          confidence: 0.85,
          boundingBox: BoundingBox(x: 100, y: 100, width: 200, height: 300),
          attributes: PersonAttributes(
            estimatedAge: 25,
            gender: 'unknown',
            clothing: ['casual'],
          ),
        ),
      ],
      objects: [
        DetectedObject(
          objectId: 'obj_1',
          className: 'car',
          confidence: 0.92,
          boundingBox: BoundingBox(x: 50, y: 200, width: 150, height: 100),
        ),
        DetectedObject(
          objectId: 'obj_2',
          className: 'tree',
          confidence: 0.78,
          boundingBox: BoundingBox(x: 300, y: 50, width: 80, height: 200),
        ),
      ],
      processingTime: 2.1,
      timestamp: DateTime.now(),
    );
  }

  Future<void> detectRealtime(String imagePath) async {
    try {
      _lastDetection = await _apiService.detectRealtime(imagePath);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearDetection() {
    _lastDetection = null;
    _error = null;
    notifyListeners();
  }
}
