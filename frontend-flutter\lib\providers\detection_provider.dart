import 'package:flutter/foundation.dart';
import '../models/detection_result.dart';
import '../services/api_service.dart';

class DetectionProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  DetectionResult? _lastDetection;
  bool _isDetecting = false;
  String? _error;

  DetectionResult? get lastDetection => _lastDetection;
  bool get isDetecting => _isDetecting;
  String? get error => _error;

  Future<void> detectObjects(String imagePath, {String? userId}) async {
    _isDetecting = true;
    _error = null;
    notifyListeners();

    try {
      // For web, we'll simulate detection since file upload is complex
      await _simulateWebDetection(userId: userId);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isDetecting = false;
      notifyListeners();
    }
  }

  Future<void> _simulateWebDetection({String? userId}) async {
    // Simulate processing time
    await Future.delayed(const Duration(seconds: 2));

    // Create mock detection result using the correct model structure
    _lastDetection = DetectionResult(
      personCount: 1,
      totalObjects: 3,
      filename: 'web_camera_${DateTime.now().millisecondsSinceEpoch}',
      persons: [
        DetectedObject(
          className: 'person',
          classId: 0,
          confidence: 0.85,
          bbox: BoundingBox(
            x1: 100.0,
            y1: 100.0,
            x2: 300.0,
            y2: 400.0,
          ),
          center: Point(x: 200.0, y: 250.0),
        ),
      ],
      allObjects: [
        DetectedObject(
          className: 'person',
          classId: 0,
          confidence: 0.85,
          bbox: BoundingBox(
            x1: 100.0,
            y1: 100.0,
            x2: 300.0,
            y2: 400.0,
          ),
          center: Point(x: 200.0, y: 250.0),
        ),
        DetectedObject(
          className: 'car',
          classId: 2,
          confidence: 0.92,
          bbox: BoundingBox(
            x1: 50.0,
            y1: 200.0,
            x2: 200.0,
            y2: 300.0,
          ),
          center: Point(x: 125.0, y: 250.0),
        ),
        DetectedObject(
          className: 'tree',
          classId: 15,
          confidence: 0.78,
          bbox: BoundingBox(
            x1: 300.0,
            y1: 50.0,
            x2: 380.0,
            y2: 250.0,
          ),
          center: Point(x: 340.0, y: 150.0),
        ),
      ],
    );
  }

  Future<void> detectRealtime(String imagePath) async {
    try {
      _lastDetection = await _apiService.detectRealtime(imagePath);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearDetection() {
    _lastDetection = null;
    _error = null;
    notifyListeners();
  }
}
