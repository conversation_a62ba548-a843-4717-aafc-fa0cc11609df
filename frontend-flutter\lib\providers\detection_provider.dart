import 'package:flutter/foundation.dart';
import '../models/detection_result.dart';
import '../services/api_service.dart';

class DetectionProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  DetectionResult? _lastDetection;
  bool _isDetecting = false;
  String? _error;

  DetectionResult? get lastDetection => _lastDetection;
  bool get isDetecting => _isDetecting;
  String? get error => _error;

  Future<void> detectObjects(String imagePath, {String? userId}) async {
    _isDetecting = true;
    _error = null;
    notifyListeners();

    try {
      _lastDetection = await _apiService.detectObjects(imagePath, userId: userId);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isDetecting = false;
      notifyListeners();
    }
  }

  Future<void> detectRealtime(String imagePath) async {
    try {
      _lastDetection = await _apiService.detectRealtime(imagePath);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearDetection() {
    _lastDetection = null;
    notifyListeners();
  }
}
