import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/voice_provider.dart';
import '../providers/detection_provider.dart';
import '../providers/user_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserAndInitialize();
    });
  }

  void _checkUserAndInitialize() async {
    final userProvider = context.read<UserProvider>();

    // If no user is logged in, redirect to welcome screen
    if (!userProvider.isLoggedIn) {
      Navigator.pushReplacementNamed(context, '/');
      return;
    }

    // Initialize speech for logged in user
    final voiceProvider = context.read<VoiceProvider>();
    await voiceProvider.initializeSpeech();

    // Welcome the user with voice
    final userName = userProvider.currentUser?.name ?? "Usuario";
    await voiceProvider.speak(
      "Hola $userName, bienvenido a Vision Assistant. "
      "Puedes decir comandos como 'detectar personas' o 'buscar objetos'. "
      "Toca el botón del micrófono para comenzar."
    );
  }

  Future<void> _handleVoiceCommand(VoiceProvider voiceProvider) async {
    final userProvider = context.read<UserProvider>();
    final detectionProvider = context.read<DetectionProvider>();

    if (voiceProvider.isListening) {
      await voiceProvider.stopListening();

      if (voiceProvider.recognizedText.isNotEmpty) {
        final text = voiceProvider.recognizedText.toLowerCase();
        final userId = userProvider.currentUser?.userId;

        // Process the voice command
        await voiceProvider.processVoiceCommand(text, userId: userId);

        // Check if it's a detection command
        if (_isDetectionCommand(text)) {
          await _performDetection();
        } else if (text.contains('ayuda')) {
          await _provideHelp();
        }
      }
    } else {
      await voiceProvider.startListening();
    }
  }

  Future<void> _performDetection() async {
    final detectionProvider = context.read<DetectionProvider>();
    final userProvider = context.read<UserProvider>();
    final voiceProvider = context.read<VoiceProvider>();

    try {
      await voiceProvider.speak("Activando cámara para detectar objetos...");

      // Navigate to camera screen for real detection
      Navigator.pushNamed(context, '/camera');

    } catch (e) {
      await voiceProvider.speak("Error al acceder a la cámara. ${e.toString()}");
    }
  }

  Future<void> _simulateDetection() async {
    final voiceProvider = context.read<VoiceProvider>();
    final userProvider = context.read<UserProvider>();

    // Simulate detection results
    await voiceProvider.speak("Procesando imagen...");
    await Future.delayed(const Duration(seconds: 1));

    // Mock detection result
    final userName = userProvider.currentUser?.name ?? "Usuario";
    await voiceProvider.speak(
      "$userName, he detectado 2 personas en la imagen. "
      "También hay 1 auto y 3 objetos más en la escena."
    );
  }

  bool _isDetectionCommand(String text) {
    final detectionKeywords = [
      'detectar', 'buscar', 'encontrar', 'ver', 'mirar',
      'personas', 'gente', 'objetos', 'cosas'
    ];

    return detectionKeywords.any((keyword) => text.contains(keyword));
  }

  Future<void> _provideHelp() async {
    final voiceProvider = context.read<VoiceProvider>();

    await voiceProvider.speak(
      "Puedes usar estos comandos: "
      "Detectar personas para buscar gente, "
      "Buscar objetos para encontrar cosas, "
      "Contar personas para saber cuántas hay. "
      "Solo toca el micrófono y habla claramente."
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vision Assistant'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.pushNamed(context, '/settings'),
            tooltip: 'Configuración',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Welcome Section
            const Icon(
              Icons.visibility,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            Consumer<UserProvider>(
              builder: (context, userProvider, child) {
                return Column(
                  children: [
                    const Text(
                      'Asistente de Visión',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                    if (userProvider.currentUser != null)
                      Text(
                        '¡Hola, ${userProvider.currentUser!.name}!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: Colors.blue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    const SizedBox(height: 10),
                    const Text(
                      'Usa comandos de voz para detectar personas y objetos',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 40),

            // Voice Control Section
            Consumer<VoiceProvider>(
              builder: (context, voiceProvider, child) {
                return Column(
                  children: [
                    // Voice Button
                    GestureDetector(
                      onTap: voiceProvider.speechEnabled
                          ? () => _handleVoiceCommand(voiceProvider)
                          : null,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: voiceProvider.isListening
                              ? Colors.red
                              : Colors.blue,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Icon(
                          voiceProvider.isListening
                              ? Icons.mic
                              : Icons.mic_none,
                          size: 50,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Status Text
                    Text(
                      voiceProvider.isListening
                          ? 'Escuchando... Di tu comando'
                          : voiceProvider.speechEnabled
                              ? 'Toca para hablar o di "detectar personas"'
                              : 'Inicializando reconocimiento de voz...',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // Recognized Text
                    if (voiceProvider.recognizedText.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Reconocido: "${voiceProvider.recognizedText}"',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],

                    // Last Command Response
                    if (voiceProvider.lastCommand?.response != null) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          voiceProvider.lastCommand!.response!,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],

                    // Error Display
                    if (voiceProvider.error != null) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Error: ${voiceProvider.error}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),

            const SizedBox(height: 40),

            // Voice Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Text(
                    'Comandos disponibles:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Consumer<UserProvider>(
                    builder: (context, userProvider, child) {
                      return Column(
                        children: userProvider.getSuggestedCommands().map((command) =>
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Text(
                              '• $command',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ).toList(),
                      );
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Detection Results
            Consumer<DetectionProvider>(
              builder: (context, detectionProvider, child) {
                if (detectionProvider.lastDetection != null) {
                  final detection = detectionProvider.lastDetection!;
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Última detección:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Personas: ${detection.personCount}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        Text(
                          'Total objetos: ${detection.totalObjects}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }
}
