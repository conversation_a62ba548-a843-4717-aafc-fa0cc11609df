import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/voice_provider.dart';
import '../providers/detection_provider.dart';
import '../providers/user_provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserAndInitialize();
    });
  }

  void _checkUserAndInitialize() {
    final userProvider = context.read<UserProvider>();

    // If no user is logged in, redirect to welcome screen
    if (!userProvider.isLoggedIn) {
      Navigator.pushReplacementNamed(context, '/');
      return;
    }

    // Initialize speech for logged in user
    context.read<VoiceProvider>().initializeSpeech();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vision Assistant'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.pushNamed(context, '/settings'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Welcome Section
            const Icon(
              Icons.visibility,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            Consumer<UserProvider>(
              builder: (context, userProvider, child) {
                return Column(
                  children: [
                    const Text(
                      'Asistente de Visión',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),
                    if (userProvider.currentUser != null)
                      Text(
                        '¡Hola, ${userProvider.currentUser!.name}!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          color: Colors.blue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    const SizedBox(height: 10),
                    const Text(
                      'Usa comandos de voz para detectar personas y objetos',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 40),

            // Voice Control Section
            Consumer<VoiceProvider>(
              builder: (context, voiceProvider, child) {
                return Column(
                  children: [
                    // Voice Button
                    GestureDetector(
                      onTap: voiceProvider.speechEnabled
                          ? (voiceProvider.isListening
                              ? voiceProvider.stopListening
                              : voiceProvider.startListening)
                          : null,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: voiceProvider.isListening
                              ? Colors.red
                              : Colors.blue,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Icon(
                          voiceProvider.isListening
                              ? Icons.mic
                              : Icons.mic_none,
                          size: 50,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Status Text
                    Text(
                      voiceProvider.isListening
                          ? 'Escuchando...'
                          : voiceProvider.speechEnabled
                              ? 'Toca para hablar'
                              : 'Inicializando...',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    // Recognized Text
                    if (voiceProvider.recognizedText.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Reconocido: "${voiceProvider.recognizedText}"',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],

                    // Last Command Response
                    if (voiceProvider.lastCommand?.response != null) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          voiceProvider.lastCommand!.response!,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],

                    // Error Display
                    if (voiceProvider.error != null) ...[
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Error: ${voiceProvider.error}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),

            const SizedBox(height: 40),

            // Camera Button
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/camera'),
              icon: const Icon(Icons.camera_alt),
              label: const Text('Abrir Cámara'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(200, 50),
              ),
            ),

            const SizedBox(height: 20),

            // Detection Results
            Consumer<DetectionProvider>(
              builder: (context, detectionProvider, child) {
                if (detectionProvider.lastDetection != null) {
                  final detection = detectionProvider.lastDetection!;
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Última detección:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Personas: ${detection.personCount}',
                          style: const TextStyle(fontSize: 16),
                        ),
                        Text(
                          'Total objetos: ${detection.totalObjects}',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }
}
