# Vision Assistant - Frontend Flutter

Aplicación móvil Flutter para asistencia visual con comandos de voz y detección de personas.

## Características

- **Comandos de voz en español**: Reconocimiento de voz para controlar la aplicación
- **Detección de personas**: Usando YOLO para detectar personas en tiempo real
- **Interfaz accesible**: Diseñada para personas con pérdida de visión
- **Respuestas de voz**: La aplicación habla los resultados de detección
- **Cámara integrada**: Captura de fotos y procesamiento en tiempo real

## Tecnologías Utilizadas

- **Flutter**: Framework de desarrollo móvil
- **Provider**: Gestión de estado
- **Camera**: Acceso a la cámara del dispositivo
- **Speech to Text**: Reconocimiento de voz
- **Flutter TTS**: Síntesis de voz
- **Dio**: Cliente HTTP para comunicación con API
- **Image Picker**: Selección de imágenes

## Estructura del Proyecto

```
lib/
├── main.dart                 # Punto de entrada de la aplicación
├── models/                   # Modelos de datos
│   ├── detection_result.dart
│   └── voice_command.dart
├── providers/                # Gestión de estado
│   ├── detection_provider.dart
│   └── voice_provider.dart
├── screens/                  # Pantallas de la aplicación
│   ├── home_screen.dart
│   ├── camera_screen.dart
│   └── settings_screen.dart
├── services/                 # Servicios de API
│   └── api_service.dart
├── widgets/                  # Widgets reutilizables
└── utils/                    # Utilidades
```

## Configuración

### Prerrequisitos

1. Flutter SDK instalado
2. Android Studio o VS Code con extensiones de Flutter
3. Dispositivo Android o emulador configurado

### Instalación

1. Instalar dependencias:
```bash
flutter pub get
```

2. Configurar permisos en `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
```

3. Ejecutar la aplicación:
```bash
flutter run
```

## Uso

1. **Pantalla Principal**:
   - Toca el botón del micrófono para activar comandos de voz
   - Di comandos como "detectar personas" o "buscar objetos"

2. **Pantalla de Cámara**:
   - Toma fotos para detectar objetos
   - Usa comandos de voz para controlar la detección
   - Escucha los resultados hablados

3. **Configuración**:
   - Verifica el estado de la conexión con el servidor
   - Consulta comandos disponibles
   - Configura opciones de voz

## Comandos de Voz Disponibles

- "detectar personas"
- "buscar objetos"
- "contar personas"
- "describir imagen"
- "ayuda"

## Configuración del Backend

Asegúrate de que el backend Python esté ejecutándose en `http://localhost:8000` antes de usar la aplicación.

## Desarrollo

### Agregar nuevas funcionalidades

1. **Nuevos modelos**: Agregar en `lib/models/`
2. **Nuevos providers**: Agregar en `lib/providers/`
3. **Nuevas pantallas**: Agregar en `lib/screens/`
4. **Nuevos servicios**: Agregar en `lib/services/`

### Testing

```bash
flutter test
```

## Problemas Conocidos

- Requiere conexión a internet para comunicarse con el backend
- Los permisos de cámara y micrófono deben ser otorgados manualmente
- El reconocimiento de voz puede variar según el dispositivo

## Contribución

1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request
