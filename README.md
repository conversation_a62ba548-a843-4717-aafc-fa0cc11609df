# Vision Assistant - Proyecto de Tesis

**Aplicación móvil basada en comandos de voz para la detección de personas con pérdida de visión**

## Descripción del Proyecto

Este proyecto de tesis desarrolla una aplicación móvil que utiliza comandos de voz en español y visión por computadora para asistir a personas con pérdida de visión en la detección de personas y objetos en su entorno.

## Arquitectura del Sistema

```
PROYECTO TESIS/
├── backend-python/          # API REST con FastAPI
│   ├── app/
│   │   ├── core/           # Configuración
│   │   ├── models/         # Modelos de datos
│   │   ├── routers/        # Endpoints de API
│   │   └── services/       # Lógica de negocio
│   ├── requirements.txt    # Dependencias Python
│   └── main.py            # Punto de entrada
│
└── frontend-flutter/       # Aplicación móvil Flutter
    ├── lib/
    │   ├── models/         # Modelos de datos
    │   ├── providers/      # Gestión de estado
    │   ├── screens/        # Pantallas de la app
    │   ├── services/       # Servicios de API
    │   └── main.dart       # Punto de entrada
    └── pubspec.yaml        # Dependencias Flutter
```

## Tecnologías Utilizadas

### Backend (Python)
- **FastAPI**: Framework web moderno y rápido
- **YOLOv8**: Modelo de detección de objetos en tiempo real
- **Whisper**: Reconocimiento de voz en español (OpenAI)
- **OpenCV**: Procesamiento de imágenes
- **PyTorch**: Framework de machine learning

### Frontend (Flutter)
- **Flutter**: Framework de desarrollo móvil multiplataforma
- **Provider**: Gestión de estado
- **Speech to Text**: Reconocimiento de voz
- **Flutter TTS**: Síntesis de voz
- **Camera**: Acceso a cámara del dispositivo

## Características Principales

### 🎤 Comandos de Voz en Español
- Reconocimiento de comandos naturales
- Respuestas habladas de los resultados
- Interfaz completamente accesible por voz

### 👁️ Detección Visual Inteligente
- Detección de personas en tiempo real
- Identificación de múltiples objetos
- Conteo automático de elementos detectados

### 📱 Interfaz Móvil Accesible
- Diseño optimizado para accesibilidad
- Botones grandes y navegación simple
- Feedback auditivo constante

### ⚡ Procesamiento en Tiempo Real
- Detección rápida usando YOLOv8
- Streaming de cámara en vivo
- Respuestas inmediatas

## Comandos de Voz Disponibles

- **"detectar personas"** - Busca personas en la imagen
- **"buscar objetos"** - Detecta todos los objetos visibles
- **"contar personas"** - Cuenta el número de personas
- **"describir imagen"** - Describe lo que ve la cámara
- **"ayuda"** - Muestra comandos disponibles

## Instalación y Configuración

### Prerrequisitos
- Python 3.8+
- Flutter SDK 3.0+
- Android Studio o VS Code
- Dispositivo Android o emulador

### 1. Configurar Backend

```bash
cd backend-python
python -m venv venv
source venv/bin/activate  # En Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Editar .env con tu configuración
python main.py
```

### 2. Configurar Frontend

```bash
cd frontend-flutter
flutter pub get
flutter run
```

## Uso de la Aplicación

1. **Iniciar el backend**: Ejecutar el servidor Python en `localhost:8000`
2. **Abrir la app móvil**: Lanzar la aplicación Flutter
3. **Dar permisos**: Permitir acceso a cámara y micrófono
4. **Usar comandos de voz**: Tocar el micrófono y hablar
5. **Tomar fotos**: Usar la cámara para detectar objetos
6. **Escuchar resultados**: La app hablará lo que detecte

## Entrenamiento del Modelo

### Opción 1: YOLO (Recomendado)
- Usar YOLOv8 pre-entrenado en COCO dataset
- Entrenar con datos personalizados si es necesario
- Optimizado para detección de personas

### Opción 2: Alternativas
- **MediaPipe**: Para detección optimizada en móviles
- **TensorFlow Lite**: Para modelos personalizados ligeros
- **Custom CNN**: Para casos de uso específicos

## Estructura de Datos

### Detección de Objetos
```json
{
  "detections": {
    "persons": [
      {
        "class_name": "person",
        "confidence": 0.95,
        "bbox": {"x1": 100, "y1": 50, "x2": 200, "y2": 300},
        "center": {"x": 150, "y": 175}
      }
    ],
    "person_count": 1,
    "total_objects": 3
  }
}
```

### Comando de Voz
```json
{
  "recognized_text": "detectar personas",
  "action": "detect_persons",
  "response": "Detectando personas en la imagen...",
  "parameters": {"filter": "person"}
}
```

## Desarrollo y Contribución

### Agregar Nuevas Funcionalidades

1. **Backend**: Agregar endpoints en `app/routers/`
2. **Frontend**: Crear nuevas pantallas en `lib/screens/`
3. **Modelos**: Definir estructuras en `models/`
4. **Servicios**: Implementar lógica en `services/`

### Testing

```bash
# Backend
cd backend-python
pytest

# Frontend
cd frontend-flutter
flutter test
```

## Roadmap del Proyecto

### Fase 1 (Actual) ✅
- [x] Estructura básica del proyecto
- [x] API REST con FastAPI
- [x] App Flutter con navegación
- [x] Integración YOLO para detección
- [x] Comandos de voz básicos

### Fase 2 (Próxima)
- [ ] Entrenamiento de modelo personalizado
- [ ] Optimización para dispositivos móviles
- [ ] Mejoras en reconocimiento de voz
- [ ] Testing exhaustivo
- [ ] Documentación completa

### Fase 3 (Futura)
- [ ] Detección de obstáculos
- [ ] Navegación asistida
- [ ] Integración con mapas
- [ ] Modo offline
- [ ] Publicación en stores

## Consideraciones de Accesibilidad

- **Navegación por voz**: Toda la app es controlable por voz
- **Feedback auditivo**: Respuestas habladas constantes
- **Botones grandes**: Interfaz optimizada para baja visión
- **Alto contraste**: Colores accesibles
- **Gestos simples**: Interacciones intuitivas

## Licencia

Este proyecto es parte de una tesis académica. Consultar con el autor para uso comercial.

## Contacto

**Autor**: [Tu Nombre]  
**Universidad**: [Tu Universidad]  
**Email**: [<EMAIL>]  
**Año**: 2024

---

*Proyecto de tesis para el grado de [Tu Carrera] - [Tu Universidad]*
