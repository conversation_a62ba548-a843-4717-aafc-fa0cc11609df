"""
User management service
"""
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Optional, List
import uuid
import json
import logging
from datetime import datetime

from app.models.database import User, DetectionSession, VoiceCommand
from app.models.schemas import UserCreate, UserUpdate, UserResponse

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self):
        pass
    
    def create_user(self, db: Session, user_data: UserCreate) -> User:
        """Create a new user"""
        try:
            db_user = User(
                user_id=str(uuid.uuid4()),
                name=user_data.name,
                preferred_language=user_data.preferred_language,
                voice_speed=user_data.voice_speed,
                created_at=datetime.utcnow(),
                last_seen=datetime.utcnow()
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
            
            logger.info(f"Created new user: {db_user.name} with ID: {db_user.user_id}")
            return db_user
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating user: {e}")
            raise Exception(f"Failed to create user: {e}")
    
    def get_user_by_id(self, db: Session, user_id: str) -> Optional[User]:
        """Get user by user_id"""
        try:
            user = db.query(User).filter(User.user_id == user_id).first()
            if user:
                # Update last_seen
                user.last_seen = datetime.utcnow()
                db.commit()
            return user
        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            return None
    
    def get_user_by_name(self, db: Session, name: str) -> Optional[User]:
        """Get user by name (case insensitive)"""
        try:
            user = db.query(User).filter(
                func.lower(User.name) == func.lower(name)
            ).first()
            if user:
                # Update last_seen
                user.last_seen = datetime.utcnow()
                db.commit()
            return user
        except Exception as e:
            logger.error(f"Error getting user by name {name}: {e}")
            return None
    
    def update_user(self, db: Session, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """Update user information"""
        try:
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return None
            
            update_data = user_update.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)
            
            user.last_seen = datetime.utcnow()
            db.commit()
            db.refresh(user)
            
            logger.info(f"Updated user {user_id}")
            return user
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating user {user_id}: {e}")
            raise Exception(f"Failed to update user: {e}")
    
    def get_all_users(self, db: Session, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all users with pagination"""
        try:
            return db.query(User).filter(User.is_active == True).offset(skip).limit(limit).all()
        except Exception as e:
            logger.error(f"Error getting all users: {e}")
            return []
    
    def deactivate_user(self, db: Session, user_id: str) -> bool:
        """Deactivate user (soft delete)"""
        try:
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return False
            
            user.is_active = False
            db.commit()
            
            logger.info(f"Deactivated user {user_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error deactivating user {user_id}: {e}")
            return False
    
    def update_voice_profile(self, db: Session, user_id: str, voice_characteristics: dict) -> bool:
        """Update user's voice profile"""
        try:
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return False
            
            user.voice_profile = json.dumps(voice_characteristics)
            user.last_seen = datetime.utcnow()
            db.commit()
            
            logger.info(f"Updated voice profile for user {user_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error updating voice profile for user {user_id}: {e}")
            return False
    
    def get_user_stats(self, db: Session, user_id: str) -> dict:
        """Get user statistics"""
        try:
            user = db.query(User).filter(User.user_id == user_id).first()
            if not user:
                return {}
            
            # Count detection sessions
            detection_count = db.query(DetectionSession).filter(
                DetectionSession.user_id == user_id
            ).count()
            
            # Count voice commands
            voice_command_count = db.query(VoiceCommand).filter(
                VoiceCommand.user_id == user_id
            ).count()
            
            # Get total persons detected
            total_persons = db.query(func.sum(DetectionSession.persons_detected)).filter(
                DetectionSession.user_id == user_id
            ).scalar() or 0
            
            return {
                "user_id": user_id,
                "name": user.name,
                "total_detections": detection_count,
                "total_voice_commands": voice_command_count,
                "total_persons_detected": total_persons,
                "member_since": user.created_at,
                "last_seen": user.last_seen
            }
            
        except Exception as e:
            logger.error(f"Error getting user stats for {user_id}: {e}")
            return {}
    
    def find_or_create_user_by_name(self, db: Session, name: str) -> tuple[User, bool]:
        """Find existing user by name or create new one. Returns (user, is_new)"""
        try:
            # Try to find existing user
            existing_user = self.get_user_by_name(db, name)
            if existing_user:
                return existing_user, False
            
            # Create new user
            user_data = UserCreate(name=name)
            new_user = self.create_user(db, user_data)
            return new_user, True
            
        except Exception as e:
            logger.error(f"Error in find_or_create_user_by_name for {name}: {e}")
            raise Exception(f"Failed to find or create user: {e}")
    
    def generate_welcome_message(self, user: User, is_new: bool) -> str:
        """Generate personalized welcome message"""
        if is_new:
            return f"¡Hola {user.name}! Bienvenido a Vision Assistant. Soy tu asistente virtual para ayudarte a detectar personas y objetos. Puedes decir comandos como 'detectar personas' o 'buscar objetos'."
        else:
            return f"¡Hola de nuevo, {user.name}! Me alegra verte. ¿En qué puedo ayudarte hoy? Puedes usar comandos de voz para detectar personas y objetos."
