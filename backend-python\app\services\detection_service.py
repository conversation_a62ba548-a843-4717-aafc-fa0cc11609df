"""
Object/Person detection service using YOLO
"""
import torch
from ultralytics import YOL<PERSON>
from PIL import Image
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

class DetectionService:
    def __init__(self):
        self.model = None
        self.load_model()
    
    def load_model(self):
        """Load YOLO model"""
        try:
            # Start with pre-trained model, can be replaced with custom trained model
            self.model = YOLO('yolov8n.pt')  # nano version for speed
            logger.info("YOLO model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {e}")
            self.model = None
    
    async def detect_objects(self, image: Image.Image, quick_mode: bool = False) -> List[Dict[str, Any]]:
        """
        Detect objects in image
        """
        if self.model is None:
            raise Exception("YOLO model not loaded")
        
        try:
            # Convert PIL image to numpy array
            img_array = np.array(image)
            
            # Configure detection parameters
            conf_threshold = settings.CONFIDENCE_THRESHOLD
            if quick_mode:
                conf_threshold = 0.3  # Lower threshold for faster detection
            
            # Run detection
            results = self.model(img_array, conf=conf_threshold)
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract detection information
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        confidence = box.conf[0].item()
                        class_id = int(box.cls[0].item())
                        class_name = self.model.names[class_id]
                        
                        detection = {
                            "class_name": class_name,
                            "class_id": class_id,
                            "confidence": round(confidence, 3),
                            "bbox": {
                                "x1": round(x1, 2),
                                "y1": round(y1, 2),
                                "x2": round(x2, 2),
                                "y2": round(y2, 2)
                            },
                            "center": {
                                "x": round((x1 + x2) / 2, 2),
                                "y": round((y1 + y2) / 2, 2)
                            }
                        }
                        detections.append(detection)
            
            # Filter for persons specifically (class_id = 0 in COCO dataset)
            persons = [d for d in detections if d["class_id"] == 0]
            
            return {
                "all_objects": detections,
                "persons": persons,
                "person_count": len(persons),
                "total_objects": len(detections)
            }
            
        except Exception as e:
            logger.error(f"Detection failed: {e}")
            raise Exception(f"Detection failed: {e}")
    
    def get_available_classes(self) -> List[str]:
        """Get list of available detection classes"""
        if self.model is None:
            return []
        return list(self.model.names.values())
    
    def get_timestamp(self) -> str:
        """Get current timestamp"""
        return datetime.utcnow().isoformat()
