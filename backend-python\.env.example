# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/vision_app_db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External APIs
OPENAI_API_KEY=your-openai-api-key-here

# File Storage
UPLOAD_FOLDER=uploads/
MAX_FILE_SIZE=10485760  # 10MB

# Model Configuration
YOLO_MODEL_PATH=models/yolo_custom.pt
CONFIDENCE_THRESHOLD=0.5
