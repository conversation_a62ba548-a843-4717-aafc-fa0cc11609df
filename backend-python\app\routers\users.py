"""
User management endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.models.database import get_db
from app.models.schemas import (
    UserCreate, UserResponse, UserUpdate, WelcomeResponse, 
    IntroductionRequest, ErrorResponse
)
from app.services.user_service import UserService
from app.services.voice_service import VoiceService

router = APIRouter()
user_service = UserService()
voice_service = VoiceService()
logger = logging.getLogger(__name__)

@router.post("/users/introduction", response_model=WelcomeResponse)
async def introduce_user(
    audio_file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Process user introduction from voice input
    """
    try:
        # Validate file type
        if not audio_file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Read audio file
        contents = await audio_file.read()
        
        # Recognize speech
        recognized_text = await voice_service.recognize_speech(contents)
        
        # Extract name from recognized text
        name = extract_name_from_text(recognized_text)
        if not name:
            raise HTTPException(
                status_code=400, 
                detail="No pude entender tu nombre. Por favor, di claramente: 'Mi nombre es [tu nombre]'"
            )
        
        # Find or create user
        user, is_new = user_service.find_or_create_user_by_name(db, name)
        
        # Generate welcome message
        welcome_message = user_service.generate_welcome_message(user, is_new)
        
        # Suggested commands
        suggested_commands = [
            "detectar personas",
            "buscar objetos", 
            "contar personas",
            "ayuda"
        ]
        
        return WelcomeResponse(
            message=welcome_message,
            is_new_user=is_new,
            user_id=user.user_id,
            suggested_commands=suggested_commands
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in user introduction: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing introduction: {str(e)}")

@router.post("/users/create", response_model=UserResponse)
async def create_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Create a new user manually
    """
    try:
        # Check if user already exists
        existing_user = user_service.get_user_by_name(db, user_data.name)
        if existing_user:
            raise HTTPException(
                status_code=400, 
                detail=f"User with name '{user_data.name}' already exists"
            )
        
        user = user_service.create_user(db, user_data)
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating user: {str(e)}")

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: str, db: Session = Depends(get_db)):
    """
    Get user by ID
    """
    try:
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting user: {str(e)}")

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str, 
    user_update: UserUpdate, 
    db: Session = Depends(get_db)
):
    """
    Update user information
    """
    try:
        user = user_service.update_user(db, user_id, user_update)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse.from_orm(user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating user: {str(e)}")

@router.get("/users", response_model=List[UserResponse])
async def get_all_users(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """
    Get all users with pagination
    """
    try:
        users = user_service.get_all_users(db, skip=skip, limit=limit)
        return [UserResponse.from_orm(user) for user in users]
        
    except Exception as e:
        logger.error(f"Error getting all users: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting users: {str(e)}")

@router.get("/users/{user_id}/stats")
async def get_user_stats(user_id: str, db: Session = Depends(get_db)):
    """
    Get user statistics
    """
    try:
        stats = user_service.get_user_stats(db, user_id)
        if not stats:
            raise HTTPException(status_code=404, detail="User not found")
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user stats for {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting user stats: {str(e)}")

@router.delete("/users/{user_id}")
async def deactivate_user(user_id: str, db: Session = Depends(get_db)):
    """
    Deactivate user (soft delete)
    """
    try:
        success = user_service.deactivate_user(db, user_id)
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        
        return {"message": "User deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error deactivating user: {str(e)}")

@router.post("/users/{user_id}/voice-profile")
async def update_voice_profile(
    user_id: str,
    audio_file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Update user's voice profile with audio sample
    """
    try:
        # Validate file type
        if not audio_file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Process audio to extract voice characteristics
        contents = await audio_file.read()
        # TODO: Implement voice characteristic extraction
        voice_characteristics = {
            "sample_processed": True,
            "file_size": len(contents),
            "content_type": audio_file.content_type
        }
        
        success = user_service.update_voice_profile(db, user_id, voice_characteristics)
        if not success:
            raise HTTPException(status_code=404, detail="User not found")
        
        return {"message": "Voice profile updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating voice profile for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating voice profile: {str(e)}")

def extract_name_from_text(text: str) -> Optional[str]:
    """
    Extract name from recognized text
    """
    text = text.lower().strip()
    
    # Common patterns for name introduction in Spanish
    patterns = [
        "mi nombre es ",
        "me llamo ",
        "soy ",
        "mi nombre ",
        "llamo ",
        "nombre es "
    ]
    
    for pattern in patterns:
        if pattern in text:
            # Extract everything after the pattern
            name_part = text.split(pattern, 1)[1].strip()
            # Take only the first few words (usually just first name)
            name_words = name_part.split()[:2]  # Max 2 words for name
            if name_words:
                return " ".join(name_words).title()
    
    # If no pattern found, try to extract from the whole text
    words = text.split()
    if len(words) >= 2 and any(word in text for word in ["nombre", "llamo", "soy"]):
        # Look for the word after these keywords
        for i, word in enumerate(words):
            if word in ["nombre", "llamo", "soy"] and i + 1 < len(words):
                return words[i + 1].title()
    
    return None
