"""
Voice recognition and processing endpoints
"""
from fastapi import API<PERSON>outer, File, UploadFile, HTTPException, Depends, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import io
import time
import uuid

from app.services.voice_service import VoiceService
from app.services.user_service import UserService
from app.models.database import get_db, VoiceCommand
from app.models.schemas import VoiceCommandResponse

router = APIRouter()
voice_service = VoiceService()
user_service = UserService()

@router.post("/voice/recognize")
async def recognize_speech(file: UploadFile = File(...)):
    """
    Recognize speech from audio file
    """
    try:
        # Validate file type
        if not file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")

        # Read audio file
        contents = await file.read()

        # Perform speech recognition
        text = await voice_service.recognize_speech(contents)

        # Process command
        command_result = await voice_service.process_voice_command(text)

        return {
            "success": True,
            "recognized_text": text,
            "command": command_result,
            "filename": file.filename
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Speech recognition failed: {str(e)}")

@router.post("/voice/command", response_model=VoiceCommandResponse)
async def process_voice_command(
    file: UploadFile = File(...),
    user_id: str = Query(None, description="User ID for personalized responses"),
    db: Session = Depends(get_db)
):
    """
    Process voice command for vision assistance with user context
    """
    start_time = time.time()
    command_id = str(uuid.uuid4())

    try:
        # Validate file type
        if not file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")

        contents = await file.read()
        text = await voice_service.recognize_speech(contents)

        # Get user context if user_id provided
        user = None
        if user_id:
            user = user_service.get_user_by_id(db, user_id)

        # Check if this is an introduction command
        if any(keyword in text.lower() for keyword in ["nombre", "llamo", "soy"]):
            result = await voice_service.process_introduction_command(text)
            command_type = "introduction"
        else:
            # Process regular vision assistance commands
            result = await voice_service.process_vision_command(text)
            command_type = result.get("action", "unknown")

            # Personalize response if user is known
            if user:
                result["response"] = voice_service.generate_personalized_response(
                    user.name, result
                )

        processing_time = time.time() - start_time

        # Save command to database if user is known
        if user_id:
            try:
                db_command = VoiceCommand(
                    command_id=command_id,
                    user_id=user_id,
                    recognized_text=text,
                    command_type=command_type,
                    response_text=result.get("response", ""),
                    action_taken=result.get("action", ""),
                    processing_time=processing_time
                )
                db.add(db_command)
                db.commit()
            except Exception as e:
                # Don't fail the request if database save fails
                print(f"Failed to save command to database: {e}")

        return VoiceCommandResponse(
            success=True,
            recognized_text=text,
            command_type=command_type,
            action=result.get("action", ""),
            response=result.get("response", ""),
            parameters=result.get("parameters", {}),
            command_id=command_id,
            user_id=user_id,
            processing_time=processing_time
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Command processing failed: {str(e)}")

@router.post("/voice/command-text", response_model=VoiceCommandResponse)
async def process_voice_command_text(
    request: dict,
    user_id: str = Query(None, description="User ID for personalized responses"),
    db: Session = Depends(get_db)
):
    """
    Process voice command from text directly (for web compatibility)
    """
    start_time = time.time()
    command_id = str(uuid.uuid4())

    try:
        text = request.get('text', '')
        if not text:
            raise HTTPException(status_code=400, detail="Text is required")

        # Get user context if user_id provided
        user = None
        if user_id:
            user = user_service.get_user_by_id(db, user_id)

        # Check if this is an introduction command
        if any(keyword in text.lower() for keyword in ["nombre", "llamo", "soy"]):
            result = await voice_service.process_introduction_command(text)
            command_type = "introduction"
        else:
            # Process regular vision assistance commands
            result = await voice_service.process_vision_command(text)
            command_type = result.get("action", "unknown")

            # Personalize response if user is known
            if user:
                result["response"] = voice_service.generate_personalized_response(
                    user.name, result
                )

        processing_time = time.time() - start_time

        # Save command to database if user is known
        if user_id:
            try:
                db_command = VoiceCommand(
                    command_id=command_id,
                    user_id=user_id,
                    recognized_text=text,
                    command_type=command_type,
                    response_text=result.get("response", ""),
                    action_taken=result.get("action", ""),
                    processing_time=processing_time
                )
                db.add(db_command)
                db.commit()
            except Exception as e:
                # Don't fail the request if database save fails
                print(f"Failed to save command to database: {e}")

        return VoiceCommandResponse(
            success=True,
            recognized_text=text,
            command_type=command_type,
            action=result.get("action", ""),
            response=result.get("response", ""),
            parameters=result.get("parameters", {}),
            command_id=command_id,
            user_id=user_id,
            processing_time=processing_time
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Command processing failed: {str(e)}")

@router.get("/voice/commands")
async def get_available_commands():
    """
    Get list of available voice commands
    """
    return {
        "commands": voice_service.get_available_commands(),
        "examples": voice_service.get_command_examples()
    }
