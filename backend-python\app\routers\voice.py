"""
Voice recognition and processing endpoints
"""
from fastapi import APIRout<PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import io

from app.services.voice_service import VoiceService

router = APIRouter()
voice_service = VoiceService()

@router.post("/voice/recognize")
async def recognize_speech(file: UploadFile = File(...)):
    """
    Recognize speech from audio file
    """
    try:
        # Validate file type
        if not file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Read audio file
        contents = await file.read()
        
        # Perform speech recognition
        text = await voice_service.recognize_speech(contents)
        
        # Process command
        command_result = await voice_service.process_voice_command(text)
        
        return {
            "success": True,
            "recognized_text": text,
            "command": command_result,
            "filename": file.filename
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Speech recognition failed: {str(e)}")

@router.post("/voice/command")
async def process_voice_command(file: UploadFile = File(...)):
    """
    Process voice command for vision assistance
    """
    try:
        contents = await file.read()
        text = await voice_service.recognize_speech(contents)
        
        # Process specific vision assistance commands
        result = await voice_service.process_vision_command(text)
        
        return {
            "success": True,
            "recognized_text": text,
            "action": result["action"],
            "response": result["response"],
            "parameters": result.get("parameters", {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Command processing failed: {str(e)}")

@router.get("/voice/commands")
async def get_available_commands():
    """
    Get list of available voice commands
    """
    return {
        "commands": voice_service.get_available_commands(),
        "examples": voice_service.get_command_examples()
    }
